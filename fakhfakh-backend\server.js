const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');

// Import services
const QuestionService = require('./services/SimpleQuestionService');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:3000", "http://localhost:3001"],
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Middleware
app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:3001"],
  credentials: true
}));
app.use(express.json());

// Game state
const rooms = new Map();
const players = new Map();

// Initialize question service
console.log('🔄 تحميل قاعدة بيانات الأسئلة...');
QuestionService.loadQuestions().then(() => {
  console.log('✅ تم تحميل قاعدة البيانات بنجاح');
}).catch(error => {
  console.error('❌ خطأ في تحميل قاعدة البيانات:', error);
});

// Room class
class GameRoom {
  constructor(hostId, settings = {}) {
    this.id = uuidv4().substring(0, 6).toUpperCase();
    this.hostId = hostId;
    this.players = new Map();
    this.currentQuestion = null;
    this.currentRound = 0;
    this.gameState = 'waiting'; // waiting, question, answering, voting, results, finished
    this.answers = new Map();
    this.votes = new Map();
    this.scores = new Map();
    this.settings = {
      maxPlayers: settings.maxPlayers || 8,
      roundTime: settings.roundTime || 30,
      totalRounds: settings.totalRounds || 5,
      categories: settings.categories || ['جميع الفئات']
    };
  }

  addPlayer(playerId, playerName) {
    if (this.players.size >= this.settings.maxPlayers) {
      return false;
    }
    
    this.players.set(playerId, {
      id: playerId,
      name: playerName,
      isHost: playerId === this.hostId,
      connected: true
    });
    
    this.scores.set(playerId, 0);
    return true;
  }

  removePlayer(playerId) {
    this.players.delete(playerId);
    this.scores.delete(playerId);
    this.answers.delete(playerId);
    this.votes.delete(playerId);
  }

  async startGame() {
    if (this.players.size < 2) return false;
    this.gameState = 'question';
    this.currentRound = 1;
    await this.nextQuestion();
    return true;
  }

  async nextQuestion() {
    this.answers.clear();
    this.votes.clear();

    try {
      // Get random question from service
      const categories = this.settings.categories || [];
      const difficulty = this.settings.difficulty || 'مختلط';

      const question = await QuestionService.getRandomQuestion(
        this.roomId,
        categories,
        difficulty
      );

      if (!question) {
        throw new Error('لا توجد أسئلة متاحة');
      }

      this.currentQuestion = {
        id: question.id,
        category: question.category,
        question: question.question,
        correctAnswer: question.correctAnswer,
        difficulty: question.difficulty
      };

      this.gameState = 'answering';
    } catch (error) {
      console.error('Error getting question:', error);
      // Fallback to a default question
      this.currentQuestion = {
        id: 1,
        category: 'معلومات عامة',
        question: 'ما هو أكبر محيط في العالم؟',
        correctAnswer: 'الهادئ',
        difficulty: 'سهل'
      };
      this.gameState = 'answering';
    }
  }

  submitAnswer(playerId, answer) {
    if (this.gameState !== 'answering') return false;
    this.answers.set(playerId, answer.trim());
    
    // Check if all players answered
    if (this.answers.size === this.players.size) {
      this.startVoting();
    }
    return true;
  }

  startVoting() {
    this.gameState = 'voting';
  }

  submitVote(playerId, votedAnswer) {
    if (this.gameState !== 'voting') return false;
    this.votes.set(playerId, votedAnswer);
    
    // Check if all players voted
    if (this.votes.size === this.players.size) {
      this.calculateScores();
    }
    return true;
  }

  calculateScores() {
    const correctAnswer = this.currentQuestion.correctAnswer.toLowerCase();
    
    // Calculate scores
    for (const [playerId, vote] of this.votes) {
      // Points for voting correctly
      if (vote.toLowerCase() === correctAnswer) {
        this.scores.set(playerId, this.scores.get(playerId) + 100);
      }
      
      // Points for getting others to vote for your answer
      for (const [voterId, votedAnswer] of this.votes) {
        if (voterId !== playerId && votedAnswer === this.answers.get(playerId)) {
          this.scores.set(playerId, this.scores.get(playerId) + 50);
        }
      }
    }
    
    this.gameState = 'results';
    
    // Check if game is finished
    setTimeout(() => {
      if (this.currentRound >= this.settings.totalRounds) {
        this.gameState = 'finished';
      } else {
        this.currentRound++;
        this.nextQuestion();
      }
    }, 5000);
  }

  getGameState() {
    return {
      id: this.id,
      gameState: this.gameState,
      currentRound: this.currentRound,
      totalRounds: this.settings.totalRounds,
      players: Array.from(this.players.values()),
      scores: Object.fromEntries(this.scores),
      currentQuestion: this.gameState === 'answering' || this.gameState === 'voting' || this.gameState === 'results' 
        ? {
            question: this.currentQuestion.question,
            category: this.currentQuestion.category
          } : null,
      answers: this.gameState === 'voting' || this.gameState === 'results' 
        ? Array.from(this.answers.values()).concat([this.currentQuestion.correctAnswer])
        : null,
      correctAnswer: this.gameState === 'results' ? this.currentQuestion.correctAnswer : null,
      votes: this.gameState === 'results' ? Object.fromEntries(this.votes) : null
    };
  }
}

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('createRoom', (data) => {
    const { playerName, settings } = data;
    const room = new GameRoom(socket.id, settings);
    room.addPlayer(socket.id, playerName);
    
    rooms.set(room.id, room);
    players.set(socket.id, { roomId: room.id, playerName });
    
    socket.join(room.id);
    socket.emit('roomCreated', { roomId: room.id, gameState: room.getGameState() });
  });

  socket.on('joinRoom', (data) => {
    const { roomId, playerName } = data;
    const room = rooms.get(roomId);
    
    if (!room) {
      socket.emit('error', { message: 'الغرفة غير موجودة' });
      return;
    }
    
    if (!room.addPlayer(socket.id, playerName)) {
      socket.emit('error', { message: 'الغرفة ممتلئة' });
      return;
    }
    
    players.set(socket.id, { roomId, playerName });
    socket.join(roomId);
    
    io.to(roomId).emit('gameStateUpdate', room.getGameState());
  });

  socket.on('startGame', async () => {
    const playerData = players.get(socket.id);
    if (!playerData) return;

    const room = rooms.get(playerData.roomId);
    if (!room || room.hostId !== socket.id) return;

    if (await room.startGame()) {
      io.to(room.id).emit('gameStateUpdate', room.getGameState());
    }
  });

  socket.on('submitAnswer', (data) => {
    const { answer } = data;
    const playerData = players.get(socket.id);
    if (!playerData) return;
    
    const room = rooms.get(playerData.roomId);
    if (!room) return;
    
    if (room.submitAnswer(socket.id, answer)) {
      io.to(room.id).emit('gameStateUpdate', room.getGameState());
    }
  });

  socket.on('submitVote', (data) => {
    const { vote } = data;
    const playerData = players.get(socket.id);
    if (!playerData) return;
    
    const room = rooms.get(playerData.roomId);
    if (!room) return;
    
    if (room.submitVote(socket.id, vote)) {
      io.to(room.id).emit('gameStateUpdate', room.getGameState());
    }
  });

  // Handle random question request for testing
  socket.on('getRandomQuestion', async (data) => {
    try {
      const question = await QuestionService.getRandomQuestion('test-room', data.category, data.difficulty);
      socket.emit('randomQuestion', question);
    } catch (error) {
      console.error('Error getting random question:', error);
      socket.emit('error', { message: 'فشل في جلب السؤال' });
    }
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
    const playerData = players.get(socket.id);
    
    if (playerData) {
      const room = rooms.get(playerData.roomId);
      if (room) {
        room.removePlayer(socket.id);
        
        if (room.players.size === 0) {
          rooms.delete(room.id);
        } else {
          io.to(room.id).emit('gameStateUpdate', room.getGameState());
        }
      }
      players.delete(socket.id);
    }
  });
});

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', rooms: rooms.size, players: players.size });
});

// Get question statistics
app.get('/api/stats', async (req, res) => {
  try {
    const stats = await QuestionService.getQuestionStats();
    const categories = await QuestionService.getAvailableCategories();

    res.json({
      success: true,
      data: {
        ...stats,
        availableCategories: categories
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'خطأ في جلب الإحصائيات'
    });
  }
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`🎮 خادم فخفخ يعمل على المنفذ ${PORT}`);
});
